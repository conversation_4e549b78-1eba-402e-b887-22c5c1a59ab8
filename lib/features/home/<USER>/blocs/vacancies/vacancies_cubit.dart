import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/apply_to_vacancy_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_vacancies_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/refer_vacancy_usecase.dart';

part 'vacancies_state.dart';

class VacanciesCubit extends Cubit<VacanciesState> {
  final GetVacanciesUseCase getVacanciesUseCase;
  final ApplyToVacancyUseCase applyToVacancyUseCase;
  final ReferVacancyUseCase referVacancyUseCase;

  VacanciesCubit({
    required this.getVacanciesUseCase,
    required this.applyToVacancyUseCase,
    required this.referVacancyUseCase,
  }) : super(VacanciesInitial());

  Future<void> fetchVacancies() async {
    emit(VacanciesLoading());

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final result = await getVacanciesUseCase.call(
          token: token,
          userId: userId,
        );

        if (result.isSuccess && result.data != null) {
          emit(VacanciesLoaded(result.data!));
        } else {
          emit(VacanciesError(result.error ?? 'Failed to fetch vacancies'));
        }
      } else {
        emit(const VacanciesError('Authentication required'));
      }
    } catch (e) {
      emit(VacanciesError(e.toString()));
    }
  }

  Future<void> applyToVacancy(int vacancyId) async {
    final currentState = state;
    if (currentState is VacanciesLoaded) {
      emit(VacancyActionInProgress(currentState.vacancies, vacancyId, 'apply'));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          final result = await applyToVacancyUseCase.call(
            token: token,
            userId: userId,
            vacancyId: vacancyId,
          );

          if (result.isSuccess) {
            emit(VacancyActionSuccess(
              currentState.vacancies,
              'Application submitted successfully!',
            ));
            // Return to loaded state after a short delay
            await Future.delayed(const Duration(seconds: 2));
            emit(VacanciesLoaded(currentState.vacancies));
          } else {
            emit(VacancyActionError(
              currentState.vacancies,
              result.error ?? 'Failed to submit application',
            ));
          }
        } else {
          emit(VacancyActionError(
            currentState.vacancies,
            'Authentication required',
          ));
        }
      } catch (e) {
        emit(VacancyActionError(currentState.vacancies, e.toString()));
      }
    }
  }

  Future<void> referVacancy(int vacancyId, String refereeEmail) async {
    final currentState = state;
    if (currentState is VacanciesLoaded) {
      emit(VacancyActionInProgress(currentState.vacancies, vacancyId, 'refer'));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          final result = await referVacancyUseCase.call(
            token: token,
            userId: userId,
            vacancyId: vacancyId,
            refereeEmail: refereeEmail,
          );

          if (result.isSuccess) {
            emit(VacancyActionSuccess(
              currentState.vacancies,
              'Referral submitted successfully!',
            ));
            // Return to loaded state after a short delay
            await Future.delayed(const Duration(seconds: 2));
            emit(VacanciesLoaded(currentState.vacancies));
          } else {
            emit(VacancyActionError(
              currentState.vacancies,
              result.error ?? 'Failed to submit referral',
            ));
          }
        } else {
          emit(VacancyActionError(
            currentState.vacancies,
            'Authentication required',
          ));
        }
      } catch (e) {
        emit(VacancyActionError(currentState.vacancies, e.toString()));
      }
    }
  }

  void clearActionStatus() {
    final currentState = state;
    if (currentState is VacancyActionSuccess ||
        currentState is VacancyActionError) {
      if (currentState is VacancyActionSuccess) {
        emit(VacanciesLoaded(currentState.vacancies));
      } else if (currentState is VacancyActionError) {
        emit(VacanciesLoaded(currentState.vacancies));
      }
    }
  }
}
