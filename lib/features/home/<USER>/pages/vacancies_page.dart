import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/vacancies/vacancies_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/vacancy_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/retry_widget.dart';

@RoutePage()
class VacanciesPage extends StatelessWidget {
  const VacanciesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<VacanciesCubit>()..fetchVacancies(),
      child: const VacanciesView(),
    );
  }
}

class VacanciesView extends StatelessWidget {
  const VacanciesView({super.key});

  void _showReferDialog(BuildContext context, int vacancyId) {
    final referEmailController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Refer a Friend'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                  'Enter the email address of the person you want to refer:'),
              const SizedBox(height: 16),
              TextField(
                controller: referEmailController,
                decoration: const InputDecoration(
                  hintText: 'Enter email address',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final email = referEmailController.text.trim();
                if (email.isNotEmpty && email.contains('@')) {
                  Navigator.of(context).pop();
                  context.read<VacanciesCubit>().referVacancy(vacancyId, email);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid email address'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Send Referral'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Vacancies',
        showBackButton: true,
      ),
      body: BlocConsumer<VacanciesCubit, VacanciesState>(
        listener: (context, state) {
          if (state is VacancyActionSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.loginGreen,
              ),
            );
          } else if (state is VacancyActionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is VacanciesLoading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            );
          } else if (state is VacanciesError) {
            return RetryWidget(
              color: AppColors.lightGrey2,
              onRetry: () {
                context.read<VacanciesCubit>().fetchVacancies();
              },
            );
          } else if (state is VacanciesLoaded ||
              state is VacancyActionInProgress ||
              state is VacancyActionSuccess ||
              state is VacancyActionError) {
            final vacancies = _getVacanciesFromState(state);

            if (vacancies.isEmpty) {
              return _buildEmptyState(context, textTheme);
            }

            return RefreshIndicator(
              color: AppColors.primaryBlue,
              onRefresh: () async {
                context.read<VacanciesCubit>().fetchVacancies();
              },
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: vacancies.length,
                itemBuilder: (context, index) {
                  final vacancy = vacancies[index];
                  final isApplying = state is VacancyActionInProgress &&
                      state.vacancyId == vacancy.id &&
                      state.action == 'apply';
                  final isReferring = state is VacancyActionInProgress &&
                      state.vacancyId == vacancy.id &&
                      state.action == 'refer';

                  return VacancyCard(
                    vacancy: vacancy,
                    isApplying: isApplying,
                    isReferring: isReferring,
                    onApply: () {
                      context.read<VacanciesCubit>().applyToVacancy(vacancy.id);
                    },
                    onRefer: () {
                      _showReferDialog(context, vacancy.id);
                    },
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  List<dynamic> _getVacanciesFromState(VacanciesState state) {
    if (state is VacanciesLoaded) {
      return state.vacancies;
    } else if (state is VacancyActionInProgress) {
      return state.vacancies;
    } else if (state is VacancyActionSuccess) {
      return state.vacancies;
    } else if (state is VacancyActionError) {
      return state.vacancies;
    }
    return [];
  }

  Widget _buildEmptyState(BuildContext context, TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.work_outline,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const SizedBox(height: 16),
            Text(
              'No Vacancies Available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontFamily: AppFonts.montserrat,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: AppColors.blackTint1,
                  ),
            ),
            const SizedBox(height: 8),
            const Text(
              'There are currently no job vacancies available. Please check back later.',
              style: TextStyle(
                fontFamily: AppFonts.montserrat,
                color: AppColors.blackTint1,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
