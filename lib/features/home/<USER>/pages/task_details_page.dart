import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_state.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/store_info_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/task_completion_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/pos_received_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/form_queue_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/overview_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/alert_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/documents_view.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/task_details_app_bar.dart';

@RoutePage()
class TaskDetailsPage extends StatelessWidget {
  final int taskId;
  final num storeId;

  /// If true, the page will open with the Alert (Brief) section expanded and selected.
  /// Defaults to false.
  final bool openBrief;

  /// If true, the page will open with the Documents section expanded and selected.
  /// Defaults to false.
  /// Note: If both [openBrief] and [openDocuments] are set to true, [openDocuments]
  /// takes precedence and the Documents section will be shown.
  final bool openDocuments;

  const TaskDetailsPage({
    super.key,
    required this.taskId,
    this.openBrief = false,
    this.openDocuments = false,
    required this.storeId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<TaskDetailsCubit>(),
      child: _TaskDetailsPageContent(
        taskId: taskId,
        openBrief: openBrief,
        openDocuments: openDocuments,
      ),
    );
  }
}

class _TaskDetailsPageContent extends StatefulWidget {
  final int taskId;
  final bool openBrief;
  final bool openDocuments;

  const _TaskDetailsPageContent({
    required this.taskId,
    this.openBrief = false,
    this.openDocuments = false,
  });

  @override
  State<_TaskDetailsPageContent> createState() =>
      _TaskDetailsPageContentState();
}

class _TaskDetailsPageContentState extends State<_TaskDetailsPageContent> {
  FormProgress? formProgress;
  late bool showAlert;
  late bool showDocuments;
  late bool showBrief;
  bool showCompleteTask = false;

  @override
  void initState() {
    super.initState();
    // Initialize the default view based on the incoming flags.
    // If both are true, Documents takes precedence.
    showDocuments = widget.openDocuments;
    showBrief = widget.openBrief;
    showAlert = false;
    // Fetch task details when the page initializes
    context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Recalculate form progress whenever dependencies change (e.g., returning from another page)
    _calculateFormProgress();
  }

  Future<void> _calculateFormProgress() async {
    final progress = await FormUtils.getFormPageProgress(
      taskId: widget.taskId,
    );
    if (mounted) {
      setState(() {
        formProgress = progress;
      });
    }
  }

  void _toggleMainView(String view) {
    setState(() {
      if (view == 'alert') {
        showAlert = !showAlert;
        if (showAlert) showDocuments = false;
      } else if (view == 'documents') {
        showDocuments = !showDocuments;
        if (showDocuments) showAlert = false;
      }
    });
  }

  Future<void> _openGoogleMaps() async {
    final state = context.read<TaskDetailsCubit>().state;
    if (state is! TaskDetailsSuccess) return;

    final task = state.taskDetail;
    try {
      double? latitude;
      double? longitude;

      if (task.taskLatitude != null && task.taskLongitude != null) {
        latitude = task.taskLatitude!.toDouble();
        longitude = task.taskLongitude!.toDouble();
      } else if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }

      if (latitude == null || longitude == null) {
        if (mounted) {
          SnackBarService.warning(
            context: context,
            message: 'Location coordinates not available for this task.',
          );
        }
        return;
      }

      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';

      context.router.push(WebBrowserRoute(url: googleMapsUrl));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening map: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, refreshState) {
        if (refreshState is RefreshForm) {
          // Recalculate form progress when refresh is triggered
          _calculateFormProgress();
        }
      },
      child: BlocBuilder<TaskDetailsCubit, TaskDetailsState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: (showAlert) ? Colors.white : AppColors.lightGrey2,
            appBar: TaskDetailsAppBar(
              task: state is TaskDetailsSuccess ? state.taskDetail : null,
              taskId: widget.taskId.toString(),
              storeId: '',
              showAlert: showAlert,
              showDocuments: showDocuments,
              onAlertTap: () => _toggleMainView('alert'),
              onDocumentsTap: () => _toggleMainView('documents'),
              onDirectionsTap: _openGoogleMaps,
            ),
            body: _buildBody(context, state),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton: Container(
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.midGrey,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 128,
                    child: AppButton(
                      text: "Complete task",
                      color: AppColors.primaryBlue,
                      textColor: Colors.white,
                      onPressed: () {
                        setState(() {
                          showCompleteTask = !showCompleteTask;
                        });
                      },
                      height: 40,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, TaskDetailsState state) {
    if (state is TaskDetailsLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is TaskDetailsError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading task details',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: () {
                context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    } else if (state is TaskDetailsSuccess) {
      final task = state.taskDetail;

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            StoreInfoView(task: task),
            showAlert
                ? Container(
                    color: Colors.white,
                    child: const Divider(),
                  )
                : const Gap(16),
            _buildPageContent(context, task),
          ],
        ),
      );
    } else {
      return const Center(
        child: Text('No task data available'),
      );
    }
  }

  Widget _buildPageContent(BuildContext context, entities.TaskDetail task) {
    if (showAlert) {
      return AlertView(task: task);
    }
    if (showDocuments || showBrief) {
      return DocumentsView(
        task: task,
        showBrief: showBrief,
        showDocuments: showDocuments,
      );
    }
    if (showCompleteTask) {
      return const CompleteTaskView();
    }
    return _buildDefaultView(context, task);
  }

  Widget _buildDefaultView(BuildContext context, entities.TaskDetail task) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: TaskCompletionView(
                  task: task,
                  formProgress: formProgress,
                ),
              ),
              const Gap(16),
              Expanded(child: PosReceivedView(task: task)),
            ],
          ),
        ),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        const Gap(8),
        FormQueueView(task: task),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        OverviewView(task: task),
        const Gap(16),
      ],
    );
  }
}

class CompleteTaskView extends StatefulWidget {
  const CompleteTaskView({super.key});

  @override
  State<CompleteTaskView> createState() => _CompleteTaskViewState();
}

class _CompleteTaskViewState extends State<CompleteTaskView> {
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _actualMinutesController =
      TextEditingController();
  int _pagesPrinted = 0;
  int _claimableKMs = 0;

  @override
  void dispose() {
    _commentsController.dispose();
    _actualMinutesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Scheduled comments section
                Text(
                  'Scheduled comments',
                  style: textTheme.montserratFormsField,
                ),
                const Gap(16),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColors.blackTint2),
                    color: Colors.white,
                  ),
                  child: TextFormField(
                    controller: _commentsController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Enter comments...',
                      hintStyle: textTheme.montserratFormsField.copyWith(
                        color: AppColors.blackTint1,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16.0),
                    ),
                    style: textTheme.bodyLarge?.copyWith(
                      color: AppColors.black,
                      fontSize: 16,
                    ),
                  ),
                ),
                const Gap(24),
                // Budgeted minutes section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Budgeted minutes',
                      style: textTheme.montserratFormsField,
                    ),
                    Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          color: AppColors.blackTint1,
                          size: 20,
                        ),
                        const Gap(8),
                        Text(
                          '30m',
                          style: textTheme.montserratTitleExtraSmall,
                        ),
                      ],
                    ),
                  ],
                ),
                const Gap(16),

                // Actual Minutes section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Actual Minutes',
                      style: textTheme.montserratFormsField,
                    ),
                    Container(
                      width: 120,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: AppColors.blackTint2),
                        // color: AppColors.lightGrey1,
                      ),
                      child: TextFormField(
                        controller: _actualMinutesController,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          hintText: '0',
                          hintStyle: textTheme.montserratTitleExtraSmall,
                          border: InputBorder.none,
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 12.0),
                        ),
                        style: textTheme.titleMedium?.copyWith(
                          color: AppColors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const Gap(16),

                // Pages printed section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Pages printed',
                      style: textTheme.montserratFormsField,
                    ),
                    _buildCounterWidget(_pagesPrinted, (value) {
                      setState(() {
                        _pagesPrinted = value;
                      });
                    }),
                  ],
                ),
                const Gap(16),

                // Claimable KMs section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Claimable KMs',
                      style: textTheme.montserratFormsField,
                    ),
                    _buildCounterWidget(_claimableKMs, (value) {
                      setState(() {
                        _claimableKMs = value;
                      });
                    }),
                  ],
                ),
              ],
            ),
          ),
        ),
        // Submit button at bottom
        const Gap(64),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16.0),
          child: AppButton(
            text: "Submit",
            color: AppColors.primaryBlue,
            textColor: Colors.white,
            onPressed: () {
              // Handle submit action
            },
            height: 56,
          ),
        ),
      ],
    );
  }

  Widget _buildCounterWidget(int value, Function(int) onChanged) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Minus button
        InkWell(
          onTap: value > 0 ? () => onChanged(value - 1) : null,
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.blackTint2),
              borderRadius: BorderRadius.circular(10.0),
              color: Colors.white,
            ),
            child: Icon(
              Icons.remove,
              color: value > 0 ? AppColors.black : AppColors.blackTint1,
              size: 16,
            ),
          ),
        ),
        // Value display
        Container(
          width: 32,
          height: 48,
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Center(
            child: Text(
              value.toString(),
              style: Theme.of(context).textTheme.montserratTitleExtraSmall,
            ),
          ),
        ),
        // Plus button
        InkWell(
          onTap: () => onChanged(value + 1),
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.blackTint2),
              borderRadius: BorderRadius.circular(10.0),
              color: Colors.white,
            ),
            child: const Icon(
              Icons.add,
              color: AppColors.black,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }
}
