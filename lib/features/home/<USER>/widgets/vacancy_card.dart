import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';

class VacancyCard extends StatelessWidget {
  final VacancyEntity vacancy;
  final VoidCallback? onApply;
  final VoidCallback? onRefer;
  final bool isApplying;
  final bool isReferring;

  const VacancyCard({
    super.key,
    required this.vacancy,
    this.onApply,
    this.onRefer,
    this.isApplying = false,
    this.isReferring = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 5,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          // TODO: Navigate to vacancy details if needed
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Job Title
              Text(
                vacancy.jobTitle,
                style: textTheme.montserratTitleSmall.copyWith(
                  color: AppColors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const Gap(10),

              // Job Location with icon
              Row(
                children: [
                  const Icon(
                    Icons.location_on_outlined,
                    size: 16,
                    color: AppColors.blackTint1,
                  ),
                  const Gap(5),
                  Expanded(
                    child: Text(
                      vacancy.jobLocation,
                      style: textTheme.montserratParagraphSmall.copyWith(
                        fontSize: 15,
                        color: AppColors.blackTint1,
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(10),

              // Job Description
              Text(
                vacancy.jobDescription,
                style: textTheme.montserratParagraphSmall.copyWith(
                  fontSize: 15,
                  color: AppColors.black,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              // Additional info if available
              if (vacancy.companyName != null ||
                  vacancy.salaryRange != null) ...[
                const Gap(8),
                if (vacancy.companyName != null)
                  Text(
                    vacancy.companyName!,
                    style: textTheme.montserratParagraphSmall.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                if (vacancy.salaryRange != null) ...[
                  const Gap(4),
                  Text(
                    vacancy.salaryRange!,
                    style: textTheme.montserratParagraphSmall.copyWith(
                      color: AppColors.blackTint1,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],

              const Gap(16),

              // Action Buttons
              Row(
                children: [
                  // Apply Button
                  if (vacancy.canApply)
                    _buildActionButton(
                      text: 'Apply',
                      backgroundColor: AppColors.loginGreen,
                      isLoading: isApplying,
                      onPressed: isApplying || isReferring ? null : onApply,
                    ),
                  if (vacancy.canApply && vacancy.canRefer) const Gap(15),

                  // Refer Button
                  if (vacancy.canRefer)
                    _buildActionButton(
                      text: 'Refer',
                      backgroundColor: AppColors.primaryBlue,
                      isLoading: isReferring,
                      onPressed: isApplying || isReferring ? null : onRefer,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String text,
    required Color backgroundColor,
    required bool isLoading,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: 80,
      height: 30,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          padding: EdgeInsets.zero,
        ),
        child: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: AppFonts.montserrat,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    );
  }
}
