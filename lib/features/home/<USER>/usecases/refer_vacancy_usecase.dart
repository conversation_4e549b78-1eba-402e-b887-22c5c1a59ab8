import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class ReferVacancyUseCase {
  final HomeRepository repository;

  ReferVacancyUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required int vacancyId,
    required String refereeEmail,
  }) async {
    return await repository.referVacancy(
      token: token,
      userId: userId,
      vacancyId: vacancyId,
      refereeEmail: refereeEmail,
    );
  }
}
