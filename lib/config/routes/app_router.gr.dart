// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i45;
import 'package:flutter/material.dart' as _i46;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i18;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i29;
import 'package:storetrack_app/features/home/<USER>/models/profile_response.dart'
    as _i49;
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart'
    as _i48;
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart'
    as _i47;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i50;
import 'package:storetrack_app/features/home/<USER>/pages/add_contact_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/add_leave_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/add_store_comment_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/auto_schedule_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/availability_page.dart'
    as _i6;
import 'package:storetrack_app/features/home/<USER>/pages/barcode_scanner_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i8;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/edit_profile_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/form_page.dart'
    as _i12;
import 'package:storetrack_app/features/home/<USER>/pages/fqpd_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/history_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/induction_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/pages/leave_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/pages/more_holder_page.dart'
    as _i20;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i21;
import 'package:storetrack_app/features/home/<USER>/pages/mpt_page.dart'
    as _i19;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/notification_page.dart'
    as _i23;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i24;
import 'package:storetrack_app/features/home/<USER>/pages/profile_holder_page.dart'
    as _i25;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i26;
import 'package:storetrack_app/features/home/<USER>/pages/qpmd_page.dart'
    as _i27;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart'
    as _i28;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i30;
import 'package:storetrack_app/features/home/<USER>/pages/signature_page.dart'
    as _i31;
import 'package:storetrack_app/features/home/<USER>/pages/skills_page.dart'
    as _i32;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i34;
import 'package:storetrack_app/features/home/<USER>/pages/store_info_page.dart'
    as _i35;
import 'package:storetrack_app/features/home/<USER>/pages/sub_header_page.dart'
    as _i36;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i37;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i38;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i40;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_pos_tasks_page.dart'
    as _i41;
import 'package:storetrack_app/features/home/<USER>/pages/useful_links_page.dart'
    as _i42;
import 'package:storetrack_app/features/home/<USER>/pages/vacancies_page.dart'
    as _i43;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i33;
import 'package:storetrack_app/features/tutorial/presentation/pages/tutorial_page.dart'
    as _i39;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i44;

/// generated route for
/// [_i1.AddContactPage]
class AddContactRoute extends _i45.PageRouteInfo<AddContactRouteArgs> {
  AddContactRoute({
    _i46.Key? key,
    _i47.StoreContactData? contact,
    bool isEditMode = false,
    required String storeId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          AddContactRoute.name,
          args: AddContactRouteArgs(
            key: key,
            contact: contact,
            isEditMode: isEditMode,
            storeId: storeId,
          ),
          initialChildren: children,
        );

  static const String name = 'AddContactRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddContactRouteArgs>();
      return _i1.AddContactPage(
        key: args.key,
        contact: args.contact,
        isEditMode: args.isEditMode,
        storeId: args.storeId,
      );
    },
  );
}

class AddContactRouteArgs {
  const AddContactRouteArgs({
    this.key,
    this.contact,
    this.isEditMode = false,
    required this.storeId,
  });

  final _i46.Key? key;

  final _i47.StoreContactData? contact;

  final bool isEditMode;

  final String storeId;

  @override
  String toString() {
    return 'AddContactRouteArgs{key: $key, contact: $contact, isEditMode: $isEditMode, storeId: $storeId}';
  }
}

/// generated route for
/// [_i2.AddLeavePage]
class AddLeaveRoute extends _i45.PageRouteInfo<void> {
  const AddLeaveRoute({List<_i45.PageRouteInfo>? children})
      : super(
          AddLeaveRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddLeaveRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i2.AddLeavePage();
    },
  );
}

/// generated route for
/// [_i3.AddStoreCommentPage]
class AddStoreCommentRoute
    extends _i45.PageRouteInfo<AddStoreCommentRouteArgs> {
  AddStoreCommentRoute({
    _i46.Key? key,
    required String taskId,
    _i48.StoreCommentData? comment,
    bool isEditMode = false,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          AddStoreCommentRoute.name,
          args: AddStoreCommentRouteArgs(
            key: key,
            taskId: taskId,
            comment: comment,
            isEditMode: isEditMode,
          ),
          initialChildren: children,
        );

  static const String name = 'AddStoreCommentRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddStoreCommentRouteArgs>();
      return _i3.AddStoreCommentPage(
        key: args.key,
        taskId: args.taskId,
        comment: args.comment,
        isEditMode: args.isEditMode,
      );
    },
  );
}

class AddStoreCommentRouteArgs {
  const AddStoreCommentRouteArgs({
    this.key,
    required this.taskId,
    this.comment,
    this.isEditMode = false,
  });

  final _i46.Key? key;

  final String taskId;

  final _i48.StoreCommentData? comment;

  final bool isEditMode;

  @override
  String toString() {
    return 'AddStoreCommentRouteArgs{key: $key, taskId: $taskId, comment: $comment, isEditMode: $isEditMode}';
  }
}

/// generated route for
/// [_i4.AssistantPage]
class AssistantRoute extends _i45.PageRouteInfo<void> {
  const AssistantRoute({List<_i45.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i4.AssistantPage();
    },
  );
}

/// generated route for
/// [_i5.AutoSchedulePage]
class AutoScheduleRoute extends _i45.PageRouteInfo<void> {
  const AutoScheduleRoute({List<_i45.PageRouteInfo>? children})
      : super(
          AutoScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'AutoScheduleRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i5.AutoSchedulePage();
    },
  );
}

/// generated route for
/// [_i6.AvailabilityPage]
class AvailabilityRoute extends _i45.PageRouteInfo<void> {
  const AvailabilityRoute({List<_i45.PageRouteInfo>? children})
      : super(
          AvailabilityRoute.name,
          initialChildren: children,
        );

  static const String name = 'AvailabilityRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i6.AvailabilityPage();
    },
  );
}

/// generated route for
/// [_i7.BarcodeScannerPage]
class BarcodeScannerRoute extends _i45.PageRouteInfo<void> {
  const BarcodeScannerRoute({List<_i45.PageRouteInfo>? children})
      : super(
          BarcodeScannerRoute.name,
          initialChildren: children,
        );

  static const String name = 'BarcodeScannerRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i7.BarcodeScannerPage();
    },
  );
}

/// generated route for
/// [_i8.DashboardHolderPage]
class DashboardHolderRoute extends _i45.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i45.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i8.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i9.DashboardPage]
class DashboardRoute extends _i45.PageRouteInfo<void> {
  const DashboardRoute({List<_i45.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i9.DashboardPage();
    },
  );
}

/// generated route for
/// [_i10.EditProfilePage]
class EditProfileRoute extends _i45.PageRouteInfo<EditProfileRouteArgs> {
  EditProfileRoute({
    _i46.Key? key,
    _i49.ProfileResponse? profileData,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          EditProfileRoute.name,
          args: EditProfileRouteArgs(
            key: key,
            profileData: profileData,
          ),
          initialChildren: children,
        );

  static const String name = 'EditProfileRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EditProfileRouteArgs>(
          orElse: () => const EditProfileRouteArgs());
      return _i10.EditProfilePage(
        key: args.key,
        profileData: args.profileData,
      );
    },
  );
}

class EditProfileRouteArgs {
  const EditProfileRouteArgs({
    this.key,
    this.profileData,
  });

  final _i46.Key? key;

  final _i49.ProfileResponse? profileData;

  @override
  String toString() {
    return 'EditProfileRouteArgs{key: $key, profileData: $profileData}';
  }
}

/// generated route for
/// [_i11.FQPDPage]
class FQPDRoute extends _i45.PageRouteInfo<FQPDRouteArgs> {
  FQPDRoute({
    _i46.Key? key,
    num? questionId,
    num? taskId,
    num? formId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          FQPDRoute.name,
          args: FQPDRouteArgs(
            key: key,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'FQPDRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<FQPDRouteArgs>(orElse: () => const FQPDRouteArgs());
      return _i11.FQPDPage(
        key: args.key,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class FQPDRouteArgs {
  const FQPDRouteArgs({
    this.key,
    this.questionId,
    this.taskId,
    this.formId,
  });

  final _i46.Key? key;

  final num? questionId;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'FQPDRouteArgs{key: $key, questionId: $questionId, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i12.FormPage]
class FormRoute extends _i45.PageRouteInfo<FormRouteArgs> {
  FormRoute({
    _i46.Key? key,
    required int taskId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          FormRoute.name,
          args: FormRouteArgs(
            key: key,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'FormRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FormRouteArgs>();
      return _i12.FormPage(
        key: args.key,
        taskId: args.taskId,
      );
    },
  );
}

class FormRouteArgs {
  const FormRouteArgs({
    this.key,
    required this.taskId,
  });

  final _i46.Key? key;

  final int taskId;

  @override
  String toString() {
    return 'FormRouteArgs{key: $key, taskId: $taskId}';
  }
}

/// generated route for
/// [_i13.HistoryPage]
class HistoryRoute extends _i45.PageRouteInfo<void> {
  const HistoryRoute({List<_i45.PageRouteInfo>? children})
      : super(
          HistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'HistoryRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i13.HistoryPage();
    },
  );
}

/// generated route for
/// [_i14.HomePage]
class HomeRoute extends _i45.PageRouteInfo<void> {
  const HomeRoute({List<_i45.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i14.HomePage();
    },
  );
}

/// generated route for
/// [_i15.InductionPage]
class InductionRoute extends _i45.PageRouteInfo<void> {
  const InductionRoute({List<_i45.PageRouteInfo>? children})
      : super(
          InductionRoute.name,
          initialChildren: children,
        );

  static const String name = 'InductionRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i15.InductionPage();
    },
  );
}

/// generated route for
/// [_i16.JourneyMapPage]
class JourneyMapRoute extends _i45.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i45.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i16.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i17.LeavePage]
class LeaveRoute extends _i45.PageRouteInfo<void> {
  const LeaveRoute({List<_i45.PageRouteInfo>? children})
      : super(
          LeaveRoute.name,
          initialChildren: children,
        );

  static const String name = 'LeaveRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i17.LeavePage();
    },
  );
}

/// generated route for
/// [_i18.LoginPage]
class LoginRoute extends _i45.PageRouteInfo<void> {
  const LoginRoute({List<_i45.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i18.LoginPage();
    },
  );
}

/// generated route for
/// [_i19.MPTPage]
class MPTRoute extends _i45.PageRouteInfo<MPTRouteArgs> {
  MPTRoute({
    _i46.Key? key,
    String? taskId,
    String? formId,
    String? questionId,
    String? questionPartId,
    String? measurementId,
    String? combineTypeId,
    String? questionPartMultiId,
    List<String>? images,
    _i50.Question? question,
    int level = 2,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          MPTRoute.name,
          args: MPTRouteArgs(
            key: key,
            taskId: taskId,
            formId: formId,
            questionId: questionId,
            questionPartId: questionPartId,
            measurementId: measurementId,
            combineTypeId: combineTypeId,
            questionPartMultiId: questionPartMultiId,
            images: images,
            question: question,
            level: level,
          ),
          initialChildren: children,
        );

  static const String name = 'MPTRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<MPTRouteArgs>(orElse: () => const MPTRouteArgs());
      return _i19.MPTPage(
        key: args.key,
        taskId: args.taskId,
        formId: args.formId,
        questionId: args.questionId,
        questionPartId: args.questionPartId,
        measurementId: args.measurementId,
        combineTypeId: args.combineTypeId,
        questionPartMultiId: args.questionPartMultiId,
        images: args.images,
        question: args.question,
        level: args.level,
      );
    },
  );
}

class MPTRouteArgs {
  const MPTRouteArgs({
    this.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
    this.images,
    this.question,
    this.level = 2,
  });

  final _i46.Key? key;

  final String? taskId;

  final String? formId;

  final String? questionId;

  final String? questionPartId;

  final String? measurementId;

  final String? combineTypeId;

  final String? questionPartMultiId;

  final List<String>? images;

  final _i50.Question? question;

  final int level;

  @override
  String toString() {
    return 'MPTRouteArgs{key: $key, taskId: $taskId, formId: $formId, questionId: $questionId, questionPartId: $questionPartId, measurementId: $measurementId, combineTypeId: $combineTypeId, questionPartMultiId: $questionPartMultiId, images: $images, question: $question, level: $level}';
  }
}

/// generated route for
/// [_i20.MoreHolderPage]
class MoreHolderRoute extends _i45.PageRouteInfo<void> {
  const MoreHolderRoute({List<_i45.PageRouteInfo>? children})
      : super(
          MoreHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreHolderRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i20.MoreHolderPage();
    },
  );
}

/// generated route for
/// [_i21.MorePage]
class MoreRoute extends _i45.PageRouteInfo<void> {
  const MoreRoute({List<_i45.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i21.MorePage();
    },
  );
}

/// generated route for
/// [_i22.NotesPage]
class NotesRoute extends _i45.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i46.Key? key,
    required _i50.TaskDetail task,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i22.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i46.Key? key;

  final _i50.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i23.NotificationsPage]
class NotificationsRoute extends _i45.PageRouteInfo<void> {
  const NotificationsRoute({List<_i45.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i23.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i24.PosPage]
class PosRoute extends _i45.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i46.Key? key,
    _i50.TaskDetail? task,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i24.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i46.Key? key;

  final _i50.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i25.ProfileHolderPage]
class ProfileHolderRoute extends _i45.PageRouteInfo<void> {
  const ProfileHolderRoute({List<_i45.PageRouteInfo>? children})
      : super(
          ProfileHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileHolderRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i25.ProfileHolderPage();
    },
  );
}

/// generated route for
/// [_i26.ProfilePage]
class ProfileRoute extends _i45.PageRouteInfo<void> {
  const ProfileRoute({List<_i45.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i26.ProfilePage();
    },
  );
}

/// generated route for
/// [_i27.QPMDPage]
class QPMDRoute extends _i45.PageRouteInfo<QPMDRouteArgs> {
  QPMDRoute({
    _i46.Key? key,
    num? questionId,
    num? questionpartId,
    num? taskId,
    num? formId,
    String? questionpartMultiId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          QPMDRoute.name,
          args: QPMDRouteArgs(
            key: key,
            questionId: questionId,
            questionpartId: questionpartId,
            taskId: taskId,
            formId: formId,
            questionpartMultiId: questionpartMultiId,
          ),
          initialChildren: children,
        );

  static const String name = 'QPMDRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<QPMDRouteArgs>(orElse: () => const QPMDRouteArgs());
      return _i27.QPMDPage(
        key: args.key,
        questionId: args.questionId,
        questionpartId: args.questionpartId,
        taskId: args.taskId,
        formId: args.formId,
        questionpartMultiId: args.questionpartMultiId,
      );
    },
  );
}

class QPMDRouteArgs {
  const QPMDRouteArgs({
    this.key,
    this.questionId,
    this.questionpartId,
    this.taskId,
    this.formId,
    this.questionpartMultiId,
  });

  final _i46.Key? key;

  final num? questionId;

  final num? questionpartId;

  final num? taskId;

  final num? formId;

  final String? questionpartMultiId;

  @override
  String toString() {
    return 'QPMDRouteArgs{key: $key, questionId: $questionId, questionpartId: $questionpartId, taskId: $taskId, formId: $formId, questionpartMultiId: $questionpartMultiId}';
  }
}

/// generated route for
/// [_i28.QuestionPage]
class QuestionRoute extends _i45.PageRouteInfo<QuestionRouteArgs> {
  QuestionRoute({
    _i46.Key? key,
    required num formId,
    num? taskId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          QuestionRoute.name,
          args: QuestionRouteArgs(
            key: key,
            formId: formId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QuestionRouteArgs>();
      return _i28.QuestionPage(
        key: args.key,
        formId: args.formId,
        taskId: args.taskId,
      );
    },
  );
}

class QuestionRouteArgs {
  const QuestionRouteArgs({
    this.key,
    required this.formId,
    this.taskId,
  });

  final _i46.Key? key;

  final num formId;

  final num? taskId;

  @override
  String toString() {
    return 'QuestionRouteArgs{key: $key, formId: $formId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i29.ResetPasswordPage]
class ResetPasswordRoute extends _i45.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i46.Key? key,
    required String email,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i29.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i46.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i30.SchedulePage]
class ScheduleRoute extends _i45.PageRouteInfo<void> {
  const ScheduleRoute({List<_i45.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i30.SchedulePage();
    },
  );
}

/// generated route for
/// [_i31.SignaturePage]
class SignatureRoute extends _i45.PageRouteInfo<SignatureRouteArgs> {
  SignatureRoute({
    _i46.Key? key,
    num? questionId,
    num? taskId,
    num? formId,
    String? title,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          SignatureRoute.name,
          args: SignatureRouteArgs(
            key: key,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'SignatureRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SignatureRouteArgs>(
          orElse: () => const SignatureRouteArgs());
      return _i31.SignaturePage(
        key: args.key,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
        title: args.title,
      );
    },
  );
}

class SignatureRouteArgs {
  const SignatureRouteArgs({
    this.key,
    this.questionId,
    this.taskId,
    this.formId,
    this.title,
  });

  final _i46.Key? key;

  final num? questionId;

  final num? taskId;

  final num? formId;

  final String? title;

  @override
  String toString() {
    return 'SignatureRouteArgs{key: $key, questionId: $questionId, taskId: $taskId, formId: $formId, title: $title}';
  }
}

/// generated route for
/// [_i32.SkillsPage]
class SkillsRoute extends _i45.PageRouteInfo<void> {
  const SkillsRoute({List<_i45.PageRouteInfo>? children})
      : super(
          SkillsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SkillsRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i32.SkillsPage();
    },
  );
}

/// generated route for
/// [_i33.SplashPage]
class SplashRoute extends _i45.PageRouteInfo<void> {
  const SplashRoute({List<_i45.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i33.SplashPage();
    },
  );
}

/// generated route for
/// [_i34.StoreHistoryPage]
class StoreHistoryRoute extends _i45.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i46.Key? key,
    required int storeId,
    required int taskId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i34.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i46.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i35.StoreInfoPage]
class StoreInfoRoute extends _i45.PageRouteInfo<StoreInfoRouteArgs> {
  StoreInfoRoute({
    _i46.Key? key,
    required String storeId,
    required String taskId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          StoreInfoRoute.name,
          args: StoreInfoRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreInfoRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreInfoRouteArgs>();
      return _i35.StoreInfoPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreInfoRouteArgs {
  const StoreInfoRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i46.Key? key;

  final String storeId;

  final String taskId;

  @override
  String toString() {
    return 'StoreInfoRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i36.SubHeaderPage]
class SubHeaderRoute extends _i45.PageRouteInfo<SubHeaderRouteArgs> {
  SubHeaderRoute({
    _i46.Key? key,
    required String title,
    num? questionId,
    num? taskId,
    num? formId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          SubHeaderRoute.name,
          args: SubHeaderRouteArgs(
            key: key,
            title: title,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'SubHeaderRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SubHeaderRouteArgs>();
      return _i36.SubHeaderPage(
        key: args.key,
        title: args.title,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class SubHeaderRouteArgs {
  const SubHeaderRouteArgs({
    this.key,
    required this.title,
    this.questionId,
    this.taskId,
    this.formId,
  });

  final _i46.Key? key;

  final String title;

  final num? questionId;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'SubHeaderRouteArgs{key: $key, title: $title, questionId: $questionId, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i37.TaskDetailsPage]
class TaskDetailsRoute extends _i45.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i46.Key? key,
    required int taskId,
    bool openBrief = false,
    bool openDocuments = false,
    required num storeId,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            taskId: taskId,
            openBrief: openBrief,
            openDocuments: openDocuments,
            storeId: storeId,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i37.TaskDetailsPage(
        key: args.key,
        taskId: args.taskId,
        openBrief: args.openBrief,
        openDocuments: args.openDocuments,
        storeId: args.storeId,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.taskId,
    this.openBrief = false,
    this.openDocuments = false,
    required this.storeId,
  });

  final _i46.Key? key;

  final int taskId;

  final bool openBrief;

  final bool openDocuments;

  final num storeId;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, taskId: $taskId, openBrief: $openBrief, openDocuments: $openDocuments, storeId: $storeId}';
  }
}

/// generated route for
/// [_i38.TodayPage]
class TodayRoute extends _i45.PageRouteInfo<void> {
  const TodayRoute({List<_i45.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i38.TodayPage();
    },
  );
}

/// generated route for
/// [_i39.TutorialPage]
class TutorialRoute extends _i45.PageRouteInfo<void> {
  const TutorialRoute({List<_i45.PageRouteInfo>? children})
      : super(
          TutorialRoute.name,
          initialChildren: children,
        );

  static const String name = 'TutorialRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i39.TutorialPage();
    },
  );
}

/// generated route for
/// [_i40.UnscheduledPage]
class UnscheduledRoute extends _i45.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i45.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i40.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i41.UnscheduledPosTasksPage]
class UnscheduledPosTasksRoute extends _i45.PageRouteInfo<void> {
  const UnscheduledPosTasksRoute({List<_i45.PageRouteInfo>? children})
      : super(
          UnscheduledPosTasksRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledPosTasksRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i41.UnscheduledPosTasksPage();
    },
  );
}

/// generated route for
/// [_i42.UsefulLinksPage]
class UsefulLinksRoute extends _i45.PageRouteInfo<void> {
  const UsefulLinksRoute({List<_i45.PageRouteInfo>? children})
      : super(
          UsefulLinksRoute.name,
          initialChildren: children,
        );

  static const String name = 'UsefulLinksRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i42.UsefulLinksPage();
    },
  );
}

/// generated route for
/// [_i43.VacanciesPage]
class VacanciesRoute extends _i45.PageRouteInfo<void> {
  const VacanciesRoute({List<_i45.PageRouteInfo>? children})
      : super(
          VacanciesRoute.name,
          initialChildren: children,
        );

  static const String name = 'VacanciesRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      return const _i43.VacanciesPage();
    },
  );
}

/// generated route for
/// [_i44.WebBrowserPage]
class WebBrowserRoute extends _i45.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i46.Key? key,
    required String url,
    List<_i45.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i45.PageInfo page = _i45.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i44.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i46.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
